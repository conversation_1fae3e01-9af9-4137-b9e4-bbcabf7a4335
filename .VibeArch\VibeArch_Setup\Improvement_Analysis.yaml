# Prompt: Detect and Recommend Codebase Improvements for Vibe Architect Using mvcd.yaml

You are acting as a system improvement agent for the Vibe Architect project.

DISREGARD ALL OTHER CONTENT AND FOCUS ON THIS TASK

---

## Objective

Your task is to analyze the current state of the Vibe Architect project and identify meaningful improvement opportunities in terms of:
1. Refactoring or removing duplicate code: Identifying and consolidating identical or very similar code snippets.
2. Modularization: Breaking down large or complex functions/components into smaller, more manageable pieces, thereby concidering the trade-off between complexity and readability.
3. Improving clarity and readability: Making code easier to understand (e.g., better variable names, comments, simpler logic).
4. Removing unused code: Deleting functions, variables, or files that are no longer needed.
5. Improving performance or efficiency: Optimizing code to run faster or use fewer resources.

---

## Input

1. Use the following file to obtain an overview of the codebase structure:

   `.VibeArch/Directory/mvcd.yaml`

   Each entry includes:
   - file: Relative path to the file
   - element: Name of the function, class, or component
   - type: One of [Component, Hook, Utility, Store, Context, Type, Other]
   - description: Description (may be missing or vague)
   - dependencies, loc, last_modified

2. Based on this metadata and your own analysis of the codebase, inspect:
   - Files with unclear or missing descriptions
   - Files with similar or duplicated functionality
   - Files with no inbound references (i.e., potentially unused code)

---

## Goal

For each problem found, return a structured improvement recommendation.

You must:
1. Analyze the actual code behind each mvcd entry
2. Identify issues in:
   - Code clarity
   - Architectural structure or component boundaries
   - Performance inefficiencies
   - Security risks
   - Redundant or duplicate functionality
   - Unused or orphaned components

3. For each issue:
   - Create a unique `issue_id` (e.g. IMP001)
   - Tag it with a `category`:
     - clarity, structure, performance, security, redundancy, semantics, unused
   - Write a short description of the issue
   - Provide a precise improvement suggestion
   - Assign a confidence score (0–100%)
   - Set an impact level: low | medium | high

---

## Output

📍 Save your output as a valid YAML list in the following file:

Name: [DATE]_improvement_suggestion.yaml
in path: `.VibeArch/Improvement/`

Format:
```yaml
- issue_id: IMP001
  file: backend/app/api/init.py
  element: InitRouter
  line: 42
  category: clarity
  issue: "Function name 'doit' is vague and uninformative."
  suggestion: "Rename to 'initialize_routes' for clarity and consistency."
  confidence: 90
  impact_level: medium

  ## Constraints

- Do not fabricate purpose or hallucinate functionality
- Do not reformat or reorder any part of the file
- 

## Final Instruction

Return only [DATE]_improvement_suggestion.yaml
