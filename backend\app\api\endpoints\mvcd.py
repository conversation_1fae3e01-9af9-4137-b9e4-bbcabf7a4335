"""
MVCD API Endpoints

This module provides API endpoints for the Minimum Viable Code Description (MVCD) workflow.
The workflow consists of three steps:
1. Analyze Codebase and Create MVCD
2. Enrich MVCD with Confidence Enhancement
3. Improvement Analysis
"""

import os
import sys
import json
import time
import threading
from typing import Dict, Any, List, Optional
from pathlib import Path
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import pyperclip

# Add the project root to the Python path
sys.path.append(os.path.abspath("."))

# Import the MVCDService and MVCDEnrichmentService
from app.services.mvcd_service import MVCDService
from app.services.mvcd_enrichment import MVCDEnrichmentService

router = APIRouter()

# Models
class MVCDStatusResponse(BaseModel):
    """Response model for MVCD status"""
    mvcd_exists: bool
    last_updated: Optional[float] = None
    total_entries: int = 0
    entries_with_descriptions: int = 0
    average_confidence: float = 0
    frontend_entries: int = 0
    backend_entries: int = 0
    frontend_confidence: float = 0
    backend_confidence: float = 0
    total_loc: int = 0
    frontend_loc: int = 0
    backend_loc: int = 0

class MVCDGenerateRequest(BaseModel):
    """Request model for generating MVCD"""
    project_path: str

class MVCDEnrichRequest(BaseModel):
    """Request model for enriching MVCD"""
    project_path: str
    coding_agent_type: str = "augment"  # Options: "cursor", "augment", "chatgpt"
    headless: bool = False
    timeout: int = 300

class MVCDAnalyzeRequest(BaseModel):
    """Request model for analyzing MVCD for improvements"""
    project_path: str

class MVCDTaskResponse(BaseModel):
    """Response model for MVCD task status"""
    task_id: str
    status: str
    message: str

# Background tasks
running_tasks = {}

def get_mvcd_path(project_path: str) -> Path:
    """Get the path to the MVCD file for a project"""
    project_root = Path(project_path).resolve()
    return project_root / ".VibeArch" / "Directory" / "mvcd.yaml"

def calculate_mvcd_metrics(project_path: str) -> Dict[str, Any]:
    """Calculate metrics from the MVCD file"""
    try:
        mvcd_service = MVCDService(project_path)
        entries = mvcd_service.load_existing_mvcd()

        if not entries:
            return {
                "mvcd_exists": False,
                "total_entries": 0,
                "entries_with_descriptions": 0,
                "average_confidence": 0,
                "frontend_entries": 0,
                "backend_entries": 0,
                "frontend_confidence": 0,
                "backend_confidence": 0,
                "total_loc": 0,
                "frontend_loc": 0,
                "backend_loc": 0
            }

        # Calculate metrics
        total_entries = len(entries)
        entries_with_descriptions = sum(1 for entry in entries if entry.get("description") and entry.get("description") != "TODO: Add description")

        # Calculate confidence metrics
        confidences = [entry.get("confidence", 0) for entry in entries if entry.get("confidence") is not None]
        average_confidence = sum(confidences) / len(confidences) if confidences else 0

        # Calculate frontend/backend metrics
        frontend_entries = [entry for entry in entries if "frontend" in entry.get("file", "").lower()]
        backend_entries = [entry for entry in entries if "backend" in entry.get("file", "").lower()]

        frontend_confidences = [entry.get("confidence", 0) for entry in frontend_entries if entry.get("confidence") is not None]
        backend_confidences = [entry.get("confidence", 0) for entry in backend_entries if entry.get("confidence") is not None]

        frontend_confidence = sum(frontend_confidences) / len(frontend_confidences) if frontend_confidences else 0
        backend_confidence = sum(backend_confidences) / len(backend_confidences) if backend_confidences else 0

        # Calculate LOC metrics
        total_loc = sum(entry.get("loc", 0) for entry in entries)
        frontend_loc = sum(entry.get("loc", 0) for entry in frontend_entries)
        backend_loc = sum(entry.get("loc", 0) for entry in backend_entries)

        # Get last updated time
        mvcd_path = get_mvcd_path(project_path)
        last_updated = os.path.getmtime(mvcd_path) if mvcd_path.exists() else None

        return {
            "mvcd_exists": True,
            "last_updated": last_updated,
            "total_entries": total_entries,
            "entries_with_descriptions": entries_with_descriptions,
            "average_confidence": average_confidence,
            "frontend_entries": len(frontend_entries),
            "backend_entries": len(backend_entries),
            "frontend_confidence": frontend_confidence,
            "backend_confidence": backend_confidence,
            "total_loc": total_loc,
            "frontend_loc": frontend_loc,
            "backend_loc": backend_loc
        }
    except Exception as e:
        print(f"Error calculating MVCD metrics: {e}")
        return {
            "mvcd_exists": False,
            "total_entries": 0,
            "entries_with_descriptions": 0,
            "average_confidence": 0,
            "frontend_entries": 0,
            "backend_entries": 0,
            "frontend_confidence": 0,
            "backend_confidence": 0,
            "total_loc": 0,
            "frontend_loc": 0,
            "backend_loc": 0
        }

@router.get("/status")
async def get_mvcd_status(project_path: str = Query(..., description="Path to the project root")):
    """Get the status of the MVCD for a project"""
    try:
        metrics = calculate_mvcd_metrics(project_path)
        return JSONResponse(content=metrics)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting MVCD status: {str(e)}")

@router.get("/content")
async def get_mvcd_content(project_path: str = Query(..., description="Path to the project root")):
    """Get the content of the MVCD file for a project"""
    try:
        mvcd_path = get_mvcd_path(project_path)

        if not mvcd_path.exists():
            raise HTTPException(status_code=404, detail="MVCD file not found")

        # Read the MVCD file
        with open(mvcd_path, "r", encoding="utf-8") as f:
            content = f.read()

        return JSONResponse(content={"content": content})
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting MVCD content: {str(e)}")

@router.post("/generate", response_model=MVCDTaskResponse)
async def generate_mvcd(request: MVCDGenerateRequest, background_tasks: BackgroundTasks):
    """Generate the initial MVCD structure for a project"""
    task_id = f"generate_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Generating MVCD..."}

    def run_mvcd_generation(project_path: str):
        try:
            mvcd_service = MVCDService(project_path)
            mvcd_service.generate()
            running_tasks[task_id] = {"status": "completed", "message": "MVCD generation completed successfully"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD generation failed: {str(e)}"}

    background_tasks.add_task(run_mvcd_generation, request.project_path)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD generation started"
    )

@router.post("/enrich", response_model=MVCDTaskResponse)
async def enrich_mvcd(request: MVCDEnrichRequest, background_tasks: BackgroundTasks):
    """Enrich the MVCD with descriptions and confidence scores using a coding agent"""
    task_id = f"enrich_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Enriching MVCD..."}

    def run_mvcd_enrichment(project_path: str, config: Dict[str, Any]):
        try:
            os.chdir(project_path)

            if config["coding_agent_type"].lower() == "augment":
                running_tasks[task_id] = {"status": "running", "message": "Using Augment automation..."}

                import subprocess
                import threading

                # Create a debug log file in a location that's definitely writable
                debug_log_path = os.path.join(project_path, "mvcd_debug.log")
                with open(debug_log_path, "w") as f:
                    f.write(f"MVCD Enrichment Debug Log - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Project path: {project_path}\n")
                    f.write(f"Coding agent: {config['coding_agent_type']}\n")
                    f.write("Starting Augment automation...\n")

                    # Check if the PowerShell script exists
                    ps_script_paths = [
                        os.path.join(project_path, "scripts", "run_mvcd_augment.ps1")  # Use our new clean script
                    ]
                    ps_script_path = None
                    for path in ps_script_paths:
                        if os.path.exists(path):
                            ps_script_path = path
                            break
                    if ps_script_path and os.path.exists(ps_script_path):
                        # Use the PowerShell script for better error handling
                        with open(debug_log_path, "a") as f:
                            f.write(f"Using PowerShell script: {ps_script_path}\n")

                        try:
                            running_tasks[task_id] = {
                                "status": "running",
                                "message": f"Running PowerShell script: {os.path.basename(ps_script_path)}..."
                            }

                            with open(debug_log_path, "a") as f:
                                f.write(f"Executing PowerShell command: powershell -ExecutionPolicy Bypass -File {ps_script_path}\n")

                            # Run the PowerShell script with output capture
                            result = subprocess.run(
                                ["powershell", "-ExecutionPolicy", "Bypass", "-File", ps_script_path],
                                capture_output=True,
                                text=True,
                                check=True
                            )

                            # Log the output
                            with open(debug_log_path, "a") as f:
                                f.write("PowerShell script output:\n")
                                f.write(result.stdout)
                                if result.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(result.stderr)

                            # Mark the task as completed after prompt is inserted
                            running_tasks[task_id] = {
                                "status": "completed",
                                "message": "Prompt inserted into Augment chat. Please check Augment for the response and wait for it to complete."
                            }
                        except subprocess.CalledProcessError as e:
                            error_msg = f"PowerShell script failed: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")
                                if e.stdout:
                                    f.write("Output:\n")
                                    f.write(e.stdout)
                                if e.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(e.stderr)

                            running_tasks[task_id] = {
                                "status": "waiting_for_user",
                                "message": f"Automation failed. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                            }
                            return
                        except Exception as e:
                            error_msg = f"Error running PowerShell script: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")

                            running_tasks[task_id] = {
                                "status": "waiting_for_user",
                                "message": f"Automation failed. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                            }
                            return
                    else:
                        # Fall back to direct AutoHotKey script execution
                        # Define possible AutoHotKey paths - prioritize the one we know exists
                        ahk_paths = [
                            r"C:\Program Files\AutoHotkey\v2\AutoHotkey64.exe",  # This is the one we know exists
                            r"C:\Program Files\AutoHotkey\v2\AutoHotkey.exe",
                            "AutoHotkey.exe"  # Try PATH as last resort
                        ]

                        # Find the first path that exists
                        ahk_path = None
                        for path in ahk_paths:
                            if os.path.exists(path):
                                ahk_path = path
                                with open(debug_log_path, "a") as f:
                                    f.write(f"Found AutoHotKey at: {path}\n")
                                break
                            else:
                                with open(debug_log_path, "a") as f:
                                    f.write(f"AutoHotKey not found at: {path}\n")

                        # If no path was found, try using the PATH environment variable
                        if not ahk_path:
                            with open(debug_log_path, "a") as f:
                                f.write(f"No AutoHotKey found in expected locations. Trying PATH...\n")
                                f.write(f"Current PATH: {os.environ.get('PATH', '')}\n")

                            # Try to find AutoHotKey in PATH
                            try:
                                # On Windows, use where.exe to find executables in PATH
                                result = subprocess.run(["where", "AutoHotkey.exe"],
                                                      capture_output=True,
                                                      text=True)
                                if result.returncode == 0:
                                    ahk_path = "AutoHotkey.exe"  # Use the one in PATH
                                    with open(debug_log_path, "a") as f:
                                        f.write(f"AutoHotKey found in PATH: {result.stdout.strip()}\n")
                                else:
                                    with open(debug_log_path, "a") as f:
                                        f.write(f"AutoHotKey not found in PATH. where.exe output: {result.stderr}\n")
                            except Exception as e:
                                with open(debug_log_path, "a") as f:
                                    f.write(f"Error checking PATH for AutoHotKey: {str(e)}\n")

                        # If still no path found, try adding the AutoHotKey directory to PATH
                        if not ahk_path:
                            ahk_dir = r"C:\Program Files\AutoHotkey\v2"
                            if os.path.exists(ahk_dir):
                                with open(debug_log_path, "a") as f:
                                    f.write(f"Adding AutoHotKey directory to PATH: {ahk_dir}\n")

                                # Add to PATH
                                os.environ["PATH"] = os.environ.get("PATH", "") + os.pathsep + ahk_dir

                                with open(debug_log_path, "a") as f:
                                    f.write(f"Updated PATH: {os.environ.get('PATH', '')}\n")

                                # Try again with the updated PATH
                                ahk_path = "AutoHotkey.exe"
                            else:
                                with open(debug_log_path, "a") as f:
                                    f.write(f"AutoHotKey directory not found at: {ahk_dir}\n")

                        # Use our improved scripts in order of preference
                        script_paths = [
                            os.path.join(project_path, "scripts", "just_paste.ahk"),  # Our new just paste script
                            os.path.join(project_path, "scripts", "multi_approach.ahk"),
                            os.path.join(project_path, "scripts", "ultra_minimal.ahk"),
                            os.path.join(project_path, "scripts", "no_window_enum.ahk"),
                            os.path.join(project_path, "scripts", "absolute_minimal.ahk")
                        ]

                        # Find the first script that exists
                        script_to_use = None
                        for script_path in script_paths:
                            if os.path.exists(script_path):
                                script_to_use = script_path
                                break

                        with open(debug_log_path, "a") as f:
                            f.write(f"No PowerShell script found. Falling back to direct AutoHotKey execution.\n")
                            f.write(f"Using AutoHotKey path: {ahk_path}\n")
                            f.write(f"Using script: {script_to_use}\n")

                        try:
                            running_tasks[task_id] = {
                                "status": "running",
                                "message": f"Running AutoHotKey script: {os.path.basename(script_to_use)}"
                            }

                            with open(debug_log_path, "a") as f:
                                f.write(f"Executing command: {ahk_path} {script_to_use}\n")

                            # Run the AutoHotKey script with output capture
                            with open(debug_log_path, "a") as f:
                                f.write(f"Running AutoHotKey command: {ahk_path} {script_to_use}\n")
                                f.write(f"Working directory: {os.getcwd()}\n")
                                f.write(f"Script exists: {os.path.exists(script_to_use)}\n")

                                # Check if the script directory exists
                                script_dir = os.path.dirname(script_to_use)
                                if script_dir:
                                    f.write(f"Script directory exists: {os.path.exists(script_dir)}\n")

                                # Check if the AutoHotKey executable exists
                                if ahk_path != "AutoHotkey.exe":  # Skip if using PATH
                                    f.write(f"AutoHotKey executable exists: {os.path.exists(ahk_path)}\n")

                            # Create a more detailed command with full paths
                            if ahk_path == "AutoHotkey.exe":
                                # When using PATH, we need to make sure the script path is absolute
                                script_abs_path = os.path.abspath(script_to_use)
                                command = [ahk_path, script_abs_path]
                            else:
                                # When using full path to executable, use the script path as is
                                command = [ahk_path, script_to_use]

                            with open(debug_log_path, "a") as f:
                                f.write(f"Final command: {command}\n")

                            # Run the command
                            result = subprocess.run(command,
                                                  capture_output=True,
                                                  text=True,
                                                  check=True)

                            # Log the output
                            with open(debug_log_path, "a") as f:
                                f.write("AutoHotKey script output:\n")
                                if result.stdout:
                                    f.write(result.stdout)
                                if result.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(result.stderr)

                            # Mark the task as completed after prompt is inserted
                            running_tasks[task_id] = {
                                "status": "completed",
                                "message": "Prompt inserted into Augment chat. Please check Augment for the response and wait for it to complete."
                            }
                        except subprocess.CalledProcessError as e:
                            error_msg = f"AutoHotKey script failed: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")
                                if hasattr(e, 'stdout') and e.stdout:
                                    f.write("Output:\n")
                                    f.write(e.stdout)
                                if hasattr(e, 'stderr') and e.stderr:
                                    f.write("\nErrors:\n")
                                    f.write(e.stderr)

                            running_tasks[task_id] = {
                                "status": "waiting_for_user",
                                "message": f"AutoHotKey script failed. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                            }
                            return
                        except Exception as e:
                            error_msg = f"Error running AutoHotKey script: {str(e)}"
                            with open(debug_log_path, "a") as f:
                                f.write(f"ERROR: {error_msg}\n")
                                f.write(f"Exception type: {type(e).__name__}\n")
                                f.write(f"Exception details: {repr(e)}\n")

                            running_tasks[task_id] = {
                                "status": "waiting_for_user",
                                "message": f"Error running AutoHotKey script. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                            }
                            return

                # If we get here, both PowerShell and AutoHotKey approaches failed
                # Fallback to the helper script for manual interaction
                helper_script_paths = [
                    os.path.join(project_path, "augment_mvcd_helper.py"),
                    os.path.join(project_path, "scripts", "augment_mvcd_helper.py")
                ]

                # Find the first script that exists
                helper_script = None
                for script_path in helper_script_paths:
                    if os.path.exists(script_path):
                        helper_script = script_path
                        break

                if helper_script:
                    # Run the helper script
                    try:
                        running_tasks[task_id] = {
                            "status": "running",
                            "message": "Launched Augment helper. Please follow the instructions in the terminal window."
                        }

                        # Run the helper script with --auto flag if possible
                        subprocess.Popen(["python", helper_script, "--auto"],
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE)

                        # We can't run this in the background because it requires user interaction
                        # So we'll just update the task status and let the user handle it
                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": "Waiting for user to complete the Augment interaction. Check your terminal for instructions."
                        }
                    except Exception as e:
                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"Error launching helper script. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                        }
                else:
                    # No helper script found, provide manual instructions
                    running_tasks[task_id] = {
                        "status": "waiting_for_user",
                        "message": "No automation scripts found. Please manually open Augment in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter."
                    }

                return
            elif config["coding_agent_type"].lower() == "cursor":
                running_tasks[task_id] = {"status": "running", "message": "Using Cursor automation..."}
                import subprocess
                import threading
                debug_log_path = os.path.join(project_path, "mvcd_debug.log")
                with open(debug_log_path, "a") as f:
                    f.write(f"MVCD Enrichment Debug Log - Cursor Workflow - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Project path: {project_path}\n")
                    f.write(f"Coding agent: {config['coding_agent_type']}\n")
                    f.write("Starting Cursor automation...\n")

                # Read the prompt file and copy to clipboard
                prompt_path = os.path.join(project_path, ".VibeArch", "VibeArch_Setup", "mvcd_description_enrichment_prompt.yaml")
                try:
                    with open(prompt_path, "r", encoding="utf-8") as prompt_file:
                        prompt_text = prompt_file.read()
                    pyperclip.copy(prompt_text)
                except Exception as e:
                    with open(debug_log_path, "a") as f:
                        f.write(f"ERROR: Failed to copy prompt to clipboard: {str(e)}\n")
                    running_tasks[task_id] = {
                        "status": "failed",
                        "message": f"Failed to copy prompt to clipboard: {str(e)}"
                    }
                    return

                # Path to the cursor_prompt.ahk script
                script_path = os.path.join(project_path, "scripts", "cursor_prompt.ahk")
                ahk_paths = [
                    r"C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey64.exe",
                    r"C:\\Program Files\\AutoHotkey\\v2\\AutoHotkey.exe",
                    "AutoHotkey.exe"
                ]
                ahk_path = None
                for path in ahk_paths:
                    if os.path.exists(path):
                        ahk_path = path
                        break
                if not ahk_path:
                    ahk_path = "AutoHotkey.exe"  # fallback to PATH

                if os.path.exists(script_path):
                    try:
                        command = [ahk_path, script_path]
                        with open(debug_log_path, "a") as f:
                            f.write(f"Executing command: {command}\n")
                        result = subprocess.run(command, capture_output=True, text=True, check=True)
                        with open(debug_log_path, "a") as f:
                            f.write("Cursor AutoHotKey script output:\n")
                            if result.stdout:
                                f.write(result.stdout)
                            if result.stderr:
                                f.write("\nErrors:\n")
                                f.write(result.stderr)
                        running_tasks[task_id] = {
                            "status": "completed",
                            "message": "Prompt inserted into Cursor chat. Please check Cursor for the response and wait for it to complete."
                        }
                    except subprocess.CalledProcessError as e:
                        error_msg = f"Cursor AutoHotKey script failed: {str(e)}"
                        with open(debug_log_path, "a") as f:
                            f.write(f"ERROR: {error_msg}\n")
                            if hasattr(e, 'stdout') and e.stdout:
                                f.write("Output:\n")
                                f.write(e.stdout)
                            if hasattr(e, 'stderr') and e.stderr:
                                f.write("\nErrors:\n")
                                f.write(e.stderr)
                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"Cursor AutoHotKey script failed. Please manually open Cursor in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                        }
                        return
                    except Exception as e:
                        error_msg = f"Error running Cursor AutoHotKey script: {str(e)}"
                        with open(debug_log_path, "a") as f:
                            f.write(f"ERROR: {error_msg}\n")
                        running_tasks[task_id] = {
                            "status": "waiting_for_user",
                            "message": f"Error running Cursor AutoHotKey script. Please manually open Cursor in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter. Error: {str(e)}"
                        }
                        return
                else:
                    running_tasks[task_id] = {
                        "status": "waiting_for_user",
                        "message": "No cursor_prompt.ahk script found. Please manually open Cursor in VSCode, paste the prompt from .VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml, and press Enter."
                    }
                    return
            else:
                # For other coding agents, use the enrichment service
                enrichment_service = MVCDEnrichmentService(config)
                success = enrichment_service.enrich()

                if success:
                    running_tasks[task_id] = {"status": "completed", "message": "MVCD enrichment completed successfully"}
                else:
                    running_tasks[task_id] = {"status": "failed", "message": "MVCD enrichment failed"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD enrichment failed: {str(e)}"}

    # Create config for the enrichment service
    config = {
        "coding_agent_type": request.coding_agent_type,
        "headless": request.headless,
        "timeout": request.timeout,
        "prompt_path": ".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml",
        "output_path": ".VibeArch/Directory/mvcd.yaml",
        "retries": 3,
        "retry_delay": 5,
        "debug": False
    }

    background_tasks.add_task(run_mvcd_enrichment, request.project_path, config)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD enrichment started"
    )

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get the status of a running MVCD task"""
    if task_id not in running_tasks:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return JSONResponse(content={
        "task_id": task_id,
        **running_tasks[task_id]
    })

@router.post("/task/{task_id}/reset")
async def reset_task(task_id: str):
    """Reset a task that might be stuck"""
    if task_id not in running_tasks:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    # Update the task status to failed
    running_tasks[task_id] = {
        "status": "failed",
        "message": "Task was manually reset by user"
    }

    return JSONResponse(content={
        "task_id": task_id,
        **running_tasks[task_id],
        "reset": True
    })

@router.post("/analyze", response_model=MVCDTaskResponse)
async def analyze_mvcd(request: MVCDAnalyzeRequest, background_tasks: BackgroundTasks):
    """Analyze the MVCD for improvement opportunities"""
    task_id = f"analyze_{os.urandom(4).hex()}"
    running_tasks[task_id] = {"status": "running", "message": "Analyzing MVCD..."}

    def run_mvcd_analysis(project_path: str):
        try:
            # TODO: Implement MVCD analysis logic
            # This would analyze the MVCD file and generate improvement suggestions
            # For now, we'll just simulate a successful analysis

            # Create the Improvement directory if it doesn't exist
            improvement_dir = Path(project_path) / ".VibeArch" / "Improvement"
            improvement_dir.mkdir(parents=True, exist_ok=True)

            # Create a placeholder improvement file
            with open(improvement_dir / "improvement_suggestions.md", "w") as f:
                f.write("# MVCD Improvement Suggestions\n\n")
                f.write("This file contains suggestions for improving the codebase based on MVCD analysis.\n\n")
                f.write("## Suggestions\n\n")
                f.write("1. Add more detailed descriptions to components with low confidence scores\n")
                f.write("2. Consider refactoring components with high LOC counts\n")
                f.write("3. Review dependencies for potential optimization\n")

            running_tasks[task_id] = {"status": "completed", "message": "MVCD analysis completed successfully"}
        except Exception as e:
            running_tasks[task_id] = {"status": "failed", "message": f"MVCD analysis failed: {str(e)}"}

    background_tasks.add_task(run_mvcd_analysis, request.project_path)

    return MVCDTaskResponse(
        task_id=task_id,
        status="running",
        message="MVCD analysis started"
    )
