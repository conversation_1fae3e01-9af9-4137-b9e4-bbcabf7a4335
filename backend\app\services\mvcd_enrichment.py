"""
MVCD Enrichment Service

This module provides functionality to automate the enrichment of MVCD (Minimum Viable Code Description)
files using a coding agent (LLM) through UI automation with <PERSON>wright.

The service:
1. Runs the MVCDService to generate/update the structure
2. Automates interaction with the coding agent's chat interface
3. Pastes the enrichment prompt into the chat input
4. Waits for and extracts the response
5. Validates and saves the updated mvcd.yaml file

Usage:
    python -m backend.app.services.mvcd_enrichment

Configuration:
    Set environment variables or use .env file:
    - CODING_AGENT_TYPE: Type of coding agent (default: "cursor", options: "cursor", "augment", "chatgpt")
    - HEADLESS: Run browser in headless mode (default: False)
    - TIMEOUT: Timeout in seconds for operations (default: 300)
    - DEBUG: Enable debug logging (default: False)
"""

import os
import sys
import time
import logging
import argparse
import yaml
from pathlib import Path
from typing import Optional, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("mvcd_enrichment")

# Default configuration
DEFAULT_CONFIG = {
    "coding_agent_type": "augment",  # Options: "cursor", "augment", "chatgpt"
    "headless": False,
    "timeout": 300,  # seconds
    "prompt_path": ".VibeArch/VibeArch_Setup/mvcd_description_enrichment_prompt.yaml",
    "output_path": ".VibeArch/Directory/mvcd.yaml",
    "retries": 3,
    "retry_delay": 5,  # seconds
    "debug": False
}

# Selectors for different coding agent interfaces
AGENT_SELECTORS = {
    "cursor": {
        "chat_input": "textarea.cursor-chat-input",
        "send_button": "button.cursor-chat-send-button",
        "response_container": ".cursor-chat-message-response",
        "code_block": "pre code",
        "loading_indicator": ".cursor-chat-loading"
    },
    "augment": {
        "chat_input": "textarea.antml-chat-textarea",
        "send_button": "button.antml-chat-submit",
        "response_container": ".antml-message-content",
        "code_block": "pre",
        "loading_indicator": ".antml-loading-indicator",
        "auth_required": True
    },
    "chatgpt": {
        "chat_input": "textarea[placeholder='Message ChatGPT…']",
        "send_button": "button[aria-label='Send message']",
        "response_container": ".markdown-content",
        "code_block": "pre",
        "loading_indicator": ".result-streaming"
    }
}


class MVCDEnrichmentService:
    """
    Service for automating the enrichment of MVCD files using a coding agent.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the MVCDEnrichmentService.

        Args:
            config: Configuration dictionary. If None, uses environment variables or defaults.
        """
        self.config = DEFAULT_CONFIG.copy()

        # Override with environment variables
        if os.environ.get("CODING_AGENT_URL"):
            self.config["coding_agent_url"] = os.environ.get("CODING_AGENT_URL")
        if os.environ.get("HEADLESS"):
            self.config["headless"] = os.environ.get("HEADLESS").lower() == "true"
        if os.environ.get("TIMEOUT"):
            self.config["timeout"] = int(os.environ.get("TIMEOUT"))

        # Override with provided config
        if config:
            self.config.update(config)

        # Convert paths to absolute paths
        self.config["prompt_path"] = Path(self.config["prompt_path"]).resolve()
        self.config["output_path"] = Path(self.config["output_path"]).resolve()

        logger.info(f"Initialized MVCDEnrichmentService with config: {self.config}")

    def run_mvcd_service(self) -> bool:
        """
        Run the MVCDService to generate/update the structure.

        Returns:
            True if successful, False otherwise.
        """
        try:
            logger.info("Running MVCDService...")
            # Import the MVCDService class
            from app.services.mvcd_service import MVCDService

            # Get the current working directory as the project path
            project_path = os.getcwd()
            logger.info(f"Using project path: {project_path}")

            # Create and run the MVCDService
            mvcd_service = MVCDService(project_path)
            mvcd_service.generate()

            logger.info("MVCDService completed successfully")
            return True
        except Exception as e:
            logger.error(f"Error running MVCDService: {e}")
            return False

    def load_prompt(self) -> Optional[str]:
        """
        Load the enrichment prompt from file.

        Returns:
            The prompt text, or None if loading failed.
        """
        try:
            logger.info(f"Loading prompt from {self.config['prompt_path']}")
            with open(self.config["prompt_path"], "r", encoding="utf-8") as f:
                prompt = f.read()
            logger.info(f"Loaded prompt ({len(prompt)} characters)")
            return prompt
        except Exception as e:
            logger.error(f"Error loading prompt: {e}")
            return None

    def validate_yaml(self, content: str) -> bool:
        """
        Validate that the content is valid YAML and has the expected structure.

        Args:
            content: YAML content to validate.

        Returns:
            True if valid, False otherwise.
        """
        try:
            data = yaml.safe_load(content)
            if not data or not isinstance(data, dict) or "codebase" not in data:
                logger.error("Invalid YAML structure: missing 'codebase' key")
                return False
            if not isinstance(data["codebase"], list):
                logger.error("Invalid YAML structure: 'codebase' is not a list")
                return False
            logger.info(f"YAML validation successful: found {len(data['codebase'])} entries")
            return True
        except Exception as e:
            logger.error(f"YAML validation failed: {e}")
            return False

    def save_yaml(self, content: str) -> bool:
        """
        Save the YAML content to the output file.

        Args:
            content: YAML content to save.

        Returns:
            True if successful, False otherwise.
        """
        try:
            # Ensure output directory exists
            self.config["output_path"].parent.mkdir(parents=True, exist_ok=True)

            # Save the file
            with open(self.config["output_path"], "w", encoding="utf-8") as f:
                f.write(content)

            logger.info(f"Saved YAML to {self.config['output_path']}")
            return True
        except Exception as e:
            logger.error(f"Error saving YAML: {e}")
            return False

    def enrich(self) -> bool:
        """
        Run the full enrichment process.

        Returns:
            True if successful, False otherwise.
        """
        # Step 1: Run MVCDService
        if not self.run_mvcd_service():
            return False

        # Step 2: Load prompt
        prompt = self.load_prompt()
        if not prompt:
            return False

        # Step 3: Interact with coding agent
        try:
            from playwright.sync_api import sync_playwright

            logger.info("Starting Playwright for coding agent interaction")
            with sync_playwright() as p:
                for attempt in range(1, self.config["retries"] + 1):
                    try:
                        logger.info(f"Attempt {attempt}/{self.config['retries']}")

                        # Launch browser
                        browser = p.chromium.launch(headless=self.config["headless"])
                        page = browser.new_page(
                            viewport={"width": 1280, "height": 800}
                        )
                        # Set default timeout for all operations
                        page.set_default_timeout(self.config["timeout"] * 1000)

                        # Navigate to coding agent
                        coding_agent_url = self.config.get("coding_agent_url")
                        if not coding_agent_url:
                            # Set default URL based on agent type
                            agent_type = self.config.get("coding_agent_type", "chatgpt").lower()
                            if agent_type == "augment":
                                # For Augment VSCode extension, we need to use a special URL
                                coding_agent_url = "vscode://augment.augment/chat"
                            elif agent_type == "cursor":
                                coding_agent_url = "https://cursor.sh/"
                            else:  # Default to ChatGPT
                                coding_agent_url = "https://chat.openai.com/"
                            logger.info(f"No coding agent URL provided, using default for {agent_type}: {coding_agent_url}")

                        # Check if this is a VSCode extension URL
                        if coding_agent_url.startswith("vscode://"):
                            logger.info(f"Detected VSCode extension URL: {coding_agent_url}")
                            logger.info("Opening VSCode extension and waiting for user to interact...")

                            # Use the system's default URL handler to open the VSCode extension
                            import webbrowser
                            webbrowser.open(coding_agent_url)

                            # Wait for the user to manually open the extension
                            logger.warning("Please manually open the Augment extension in VSCode")
                            logger.warning("The script will continue in 10 seconds...")
                            time.sleep(10)

                            # Since we can't directly control the VSCode extension with Playwright,
                            # we'll need to use a different approach
                            logger.error("Direct automation of VSCode extensions is not supported")
                            logger.error("Please manually paste the prompt into the Augment chat")
                            logger.error("The prompt has been copied to the clipboard")

                            # Copy the prompt to the clipboard
                            try:
                                import pyperclip
                                pyperclip.copy(prompt)
                                logger.info("Prompt copied to clipboard")
                            except ImportError:
                                logger.error("pyperclip not installed. Install with: pip install pyperclip")

                            # Wait for the user to manually paste and send the prompt
                            logger.warning("Please manually paste the prompt into the Augment chat")
                            logger.warning("Then press Enter to send it")
                            logger.warning("The script will wait for 5 minutes for you to complete this action")

                            # Since we can't continue with automation, we'll exit with an error
                            return False
                        else:
                            # For web-based agents, we can use Playwright as normal
                            logger.info(f"Navigating to {coding_agent_url}")
                            page.goto(coding_agent_url)

                        # Check if authentication is required
                        agent_type = self.config.get("coding_agent_type", "chatgpt").lower()
                        selectors = AGENT_SELECTORS.get(agent_type, AGENT_SELECTORS["chatgpt"])

                        # Add a warning about authentication
                        if selectors.get("auth_required", False):
                            logger.warning(f"NOTE: You need to be logged in to {agent_type} for this to work.")
                            logger.warning(f"The browser will open and wait for you to log in if needed.")

                        # Wait for chat interface to load
                        logger.info(f"Waiting for {agent_type} chat interface...")
                        page.wait_for_selector(selectors["chat_input"],
                                              timeout=self.config["timeout"] * 1000)

                        # Insert prompt
                        logger.info("Inserting prompt...")
                        page.fill(selectors["chat_input"], prompt)
                        page.click(selectors["send_button"])

                        # Wait for response
                        logger.info("Waiting for response...")
                        page.wait_for_selector(selectors["response_container"],
                                              timeout=self.config["timeout"] * 1000)

                        # Give some time for the full response to load
                        time.sleep(5)

                        # Extract YAML content
                        logger.info("Extracting YAML content...")

                        yaml_content = page.evaluate(f"""() => {{
                            const elements = document.querySelectorAll('{selectors["response_container"]} {selectors["code_block"]}');
                            for (const el of elements) {{
                                if (el.textContent.startsWith('codebase:')) {{
                                    return el.textContent;
                                }}
                            }}
                            return null;
                        }}""")

                        # Close browser
                        browser.close()

                        # Validate and save YAML
                        if yaml_content:
                            if self.validate_yaml(yaml_content):
                                return self.save_yaml(yaml_content)
                            else:
                                logger.error("Invalid YAML content received")
                        else:
                            logger.error("Failed to extract YAML content from response")

                        # If we got here, something went wrong
                        if attempt < self.config["retries"]:
                            logger.info(f"Retrying in {self.config['retry_delay']} seconds...")
                            time.sleep(self.config["retry_delay"])

                    except Exception as e:
                        logger.error(f"Error during attempt {attempt}: {e}")
                        if attempt < self.config["retries"]:
                            logger.info(f"Retrying in {self.config['retry_delay']} seconds...")
                            time.sleep(self.config["retry_delay"])

                logger.error(f"Failed after {self.config['retries']} attempts")
                return False

        except ImportError:
            logger.error("Playwright not installed. Run: pip install playwright")
            logger.error("Then run: playwright install")
            return False
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return False


def main():
    """
    Main entry point when running as a script.
    """
    parser = argparse.ArgumentParser(description="MVCD Enrichment Service")
    parser.add_argument("--url", help="URL of the coding agent")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    parser.add_argument("--timeout", type=int, help="Timeout in seconds")
    args = parser.parse_args()

    # Build config from args
    config = {}
    if args.url:
        config["coding_agent_url"] = args.url
    if args.headless:
        config["headless"] = True
    if args.timeout:
        config["timeout"] = args.timeout

    # Run enrichment
    service = MVCDEnrichmentService(config)
    success = service.enrich()

    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
