import { useState, useEffect } from 'react';
import { useProject } from '../hooks/useProject';
import { useCodingAgent } from '../contexts/CodingAgentContext';
import api from '../services/api';
import yaml from 'js-yaml';

const MVCD = () => {
  const { currentProject } = useProject();
  const { codingAgent, setCodingAgent } = useCodingAgent();
  const [mvcdStatus, setMvcdStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tasks, setTasks] = useState({
    generate: null,
    enrich: null,
    analyze: null
  });
  const [activeTab, setActiveTab] = useState('overview');

  // New state variables for the code base view
  const [mvcdData, setMvcdData] = useState(null);
  const [mvcdLoading, setMvcdLoading] = useState(false);
  const [mvcdError, setMvcdError] = useState(null);
  const [directoryStructure, setDirectoryStructure] = useState([]);
  const [selectedDirectory, setSelectedDirectory] = useState('/');

  // New state variables for the ignore tab
  const [ignoreData, setIgnoreData] = useState(null);
  const [ignoreLoading, setIgnoreLoading] = useState(false);
  const [ignoreError, setIgnoreError] = useState(null);
  const [ignoreModified, setIgnoreModified] = useState(false);

  // Load MVCD status when component mounts or project changes
  useEffect(() => {
    // Check if we have a current project
    if (currentProject?.path) {
      console.log('MVCD: Current project found in context:', currentProject.path);
      loadMvcdStatus();
    } else {
      console.log('MVCD: No current project in context, checking localStorage');
      // Try to load from localStorage as a fallback
      const savedProject = localStorage.getItem('currentProject');
      if (savedProject) {
        try {
          const projectData = JSON.parse(savedProject);
          console.log('MVCD: Found project in localStorage:', projectData.path);
          // Use the project from localStorage for this component
          loadMvcdStatus(projectData);
        } catch (err) {
          console.error('MVCD: Error parsing saved project:', err);
          setLoading(false);
        }
      } else {
        console.log('MVCD: No project found in localStorage');
        setLoading(false);
      }
    }
  }, [currentProject]);

  // Poll for task status updates
  useEffect(() => {
    // Only poll for tasks that are in a running state (not waiting for user)
    const taskIds = Object.values(tasks)
      .filter(task => task && task.status === 'running')
      .map(task => task.task_id);

    if (taskIds.length === 0) return;

    const interval = setInterval(async () => {
      // Get the active project for status updates
      const activeProject = getActiveProject();

      for (const taskId of taskIds) {
        try {
          console.log(`MVCD: Polling task status for task ${taskId}`);
          const response = await api.get(`/mvcd/task/${taskId}`);
          const taskData = response.data;

          // Update the task status
          setTasks(prev => {
            const newTasks = { ...prev };

            // Find which task this is
            for (const [key, task] of Object.entries(prev)) {
              if (task && task.task_id === taskId) {
                newTasks[key] = taskData;

                // If task completed, failed, or waiting for user, reload MVCD status
                if (taskData.status === 'completed' || taskData.status === 'failed' || taskData.status === 'waiting_for_user') {
                  console.log(`MVCD: Task ${taskId} ${taskData.status}, reloading status`);
                  // Use the active project when reloading status
                  if (activeProject) {
                    loadMvcdStatus(activeProject);
                  }
                }

                break;
              }
            }

            return newTasks;
          });
        } catch (err) {
          console.error(`MVCD: Error polling task ${taskId}:`, err);
        }
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [tasks]); // getActiveProject and loadMvcdStatus are defined in the component, so they don't need to be dependencies

  // Load MVCD data when component mounts or project changes
  useEffect(() => {
    if (activeTab === 'codebase' && mvcdStatus?.mvcd_exists && !mvcdData) {
      fetchMvcdData();
    }
  }, [activeTab, mvcdStatus, mvcdData]);

  // Load ignore data when ignore tab is selected
  useEffect(() => {
    if (activeTab === 'ignore' && !ignoreData) {
      fetchIgnoreData();
    }
  }, [activeTab, ignoreData]);

  const loadMvcdStatus = async (projectOverride = null) => {
    // Use the provided project override or fall back to the current project
    const projectToUse = projectOverride || currentProject;

    if (!projectToUse?.path) {
      console.error('MVCD: Cannot load status - no project path available');
      setError('No project selected. Please select a project first.');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('MVCD: Loading status for project:', projectToUse.path);
      const response = await api.get(`/mvcd/status?project_path=${encodeURIComponent(projectToUse.path)}`);
      setMvcdStatus(response.data);
      console.log('MVCD: Status loaded successfully:', response.data);
    } catch (err) {
      console.error('MVCD: Error loading status:', err);
      setError(`Error loading MVCD status: ${err.message || err}`);
    } finally {
      setLoading(false);
    }
  };

  // Get the current active project (either from context or localStorage)
  const getActiveProject = () => {
    if (currentProject?.path) {
      return currentProject;
    }

    // Try to get from localStorage
    const savedProject = localStorage.getItem('currentProject');
    if (savedProject) {
      try {
        return JSON.parse(savedProject);
      } catch (err) {
        console.error('MVCD: Error parsing saved project:', err);
        return null;
      }
    }

    return null;
  };

  const handleGenerateMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Generating MVCD for project:', activeProject.path);
      const response = await api.post('/mvcd/generate', {
        project_path: activeProject.path
      });

      setTasks(prev => ({
        ...prev,
        generate: response.data
      }));
    } catch (err) {
      console.error('MVCD: Error generating MVCD:', err);
      setError(`Error generating MVCD: ${err.message || err}`);
    }
  };

  const handleEnrichMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Enriching MVCD for project:', activeProject.path, 'with agent:', codingAgent);
      const response = await api.post('/mvcd/enrich', {
        project_path: activeProject.path,
        coding_agent_type: codingAgent, // Use the selected coding agent
        headless: false,
        timeout: 300
      });

      setTasks(prev => ({
        ...prev,
        enrich: response.data
      }));
    } catch (err) {
      console.error('MVCD: Error enriching MVCD:', err);
      setError(`Error enriching MVCD: ${err.message || err}`);
    }
  };

  const handleAnalyzeMvcd = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setError('No project selected. Please select a project first.');
      return;
    }

    try {
      console.log('MVCD: Analyzing MVCD for project:', activeProject.path);
      const response = await api.post('/mvcd/analyze', {
        project_path: activeProject.path
      });

      setTasks(prev => ({
        ...prev,
        analyze: response.data
      }));
    } catch (err) {
      console.error('MVCD: Error analyzing MVCD:', err);
      setError(`Error analyzing MVCD: ${err.message || err}`);
    }
  };

  // Render status indicator
  const renderStatusIndicator = (status, overrideStatus = null) => {
    // If there's an override status, use that instead
    const effectiveStatus = overrideStatus || status;

    if (effectiveStatus === 'running') {
      return <div className="h-3 w-3 rounded-full bg-yellow-500"></div>;
    } else if (effectiveStatus === 'waiting_for_user') {
      return <div className="h-3 w-3 rounded-full bg-blue-500 animate-pulse"></div>;
    } else if (effectiveStatus === 'completed') {
      return <div className="h-3 w-3 rounded-full bg-green-500"></div>;
    } else if (effectiveStatus === 'failed') {
      return <div className="h-3 w-3 rounded-full bg-red-500"></div>;
    } else {
      return <div className="h-3 w-3 rounded-full bg-gray-300"></div>;
    }
  };

  // Format date
  const formatDate = (timestamp) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp * 1000);
    // Format as YYYY-MM-DD
    return date.toISOString().split('T')[0];
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${Math.round(value)}%`;
  };

  // Handle coding agent selection change
  const handleCodingAgentChange = (e) => {
    const newAgent = e.target.value;
    setCodingAgent(newAgent);
    // Save preference to localStorage
    localStorage.setItem('preferredCodingAgent', newAgent);
  };

  // Get the active project name for display
  const getActiveProjectName = () => {
    const activeProject = getActiveProject();
    if (activeProject?.name) {
      return activeProject.name;
    }
    if (activeProject?.path) {
      // Extract name from path if no name is available
      return activeProject.path.split('/').pop();
    }
    return null;
  };

  // Handle project selection if no project is loaded
  const handleProjectSelection = () => {
    // Navigate to projects page
    window.location.href = '/projects';
  };

  // Fetch MVCD data from the server
  const fetchMvcdData = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setMvcdError('No project selected. Please select a project first.');
      return;
    }

    try {
      setMvcdLoading(true);
      setMvcdError(null);

      // Get the MVCD file path
      const mvcdPath = `${activeProject.path}/.VibeArch/Directory/mvcd.yaml`;
      console.log('Fetching MVCD data from:', mvcdPath);

      // Fetch the MVCD file content
      const response = await api.get(`/mvcd/content?project_path=${encodeURIComponent(activeProject.path)}`);

      if (response.data && response.data.content) {
        // Parse YAML content
        const parsedData = yaml.load(response.data.content);
        setMvcdData(parsedData);
        console.log('MVCD data loaded successfully:', parsedData);

        // Extract directory structure from the MVCD data
        buildDirectoryStructure(parsedData);
      } else {
        throw new Error('Invalid MVCD data format');
      }
    } catch (err) {
      console.error('Error fetching MVCD data:', err);
      setMvcdError(`Error fetching MVCD data: ${err.message || err}`);
    } finally {
      setMvcdLoading(false);
    }
  };

  const fetchIgnoreData = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setIgnoreError('No project selected. Please select a project first.');
      return;
    }

    try {
      setIgnoreLoading(true);
      setIgnoreError(null);

      // Fetch the ignore file content
      const response = await api.get(`/mvcd/ignore?project_path=${encodeURIComponent(activeProject.path)}`);

      if (response.data && response.data.content) {
        // Parse YAML content
        const parsedData = yaml.load(response.data.content);
        if (parsedData && parsedData.ignore && Array.isArray(parsedData.ignore)) {
          setIgnoreData(parsedData.ignore.map((pattern, index) => ({
            id: index,
            pattern: pattern,
            enabled: true // All patterns are enabled by default
          })));
          console.log('Ignore data loaded successfully:', parsedData);
        } else {
          throw new Error('Invalid ignore file format');
        }
      } else {
        throw new Error('Ignore file not found or empty');
      }
    } catch (err) {
      console.error('Error fetching ignore data:', err);
      setIgnoreError(`Error loading ignore data: ${err.message || err}`);
    } finally {
      setIgnoreLoading(false);
    }
  };

  const handleIgnoreToggle = (id) => {
    setIgnoreData(prev =>
      prev.map(item =>
        item.id === id ? { ...item, enabled: !item.enabled } : item
      )
    );
    setIgnoreModified(true);
  };

  const handleSaveIgnoreChanges = async () => {
    const activeProject = getActiveProject();

    if (!activeProject?.path) {
      setIgnoreError('No project selected. Please select a project first.');
      return;
    }

    try {
      // Create the YAML structure
      const yamlData = {
        ignore: ignoreData.filter(item => item.enabled).map(item => item.pattern)
      };

      // Convert to YAML string
      const yamlContent = yaml.dump(yamlData);

      // Save the ignore file
      const response = await api.post('/mvcd/ignore', {
        project_path: activeProject.path,
        content: yamlContent
      });

      if (response.data && response.data.success) {
        setIgnoreModified(false);
        console.log('Ignore file saved successfully');
      } else {
        throw new Error('Failed to save ignore file');
      }
    } catch (err) {
      console.error('Error saving ignore data:', err);
      setIgnoreError(`Error saving ignore data: ${err.message || err}`);
    }
  };

  // Build directory structure from MVCD data
  const buildDirectoryStructure = (data) => {
    if (!data || !data.codebase || !Array.isArray(data.codebase)) {
      setDirectoryStructure([]);
      return;
    }

    // Get all unique directories from file paths
    const directories = new Set();
    directories.add('/'); // Add root directory

    data.codebase.forEach(item => {
      if (item.file) {
        // Split the file path into directories
        const parts = item.file.split('/');
        let currentPath = '';

        // Add each directory level
        for (let i = 0; i < parts.length - 1; i++) {
          currentPath += (currentPath ? '/' : '') + parts[i];
          directories.add(currentPath);
        }
      }
    });

    // Convert to array and sort
    const sortedDirs = Array.from(directories).sort((a, b) => {
      // Count slashes to determine depth
      const depthA = (a.match(/\//g) || []).length;
      const depthB = (b.match(/\//g) || []).length;

      // Sort by depth first, then alphabetically
      if (depthA !== depthB) return depthA - depthB;
      return a.localeCompare(b);
    });

    setDirectoryStructure(sortedDirs);
  };

  // Handle directory selection change
  const handleDirectoryChange = (e) => {
    setSelectedDirectory(e.target.value);
  };

  // Get indentation level for a file path
  const getIndentationLevel = (filePath) => {
    if (!filePath) return 0;

    // Count the number of directory separators
    return (filePath.match(/\//g) || []).length;
  };

  // Get color based on directory depth
  const getDirectoryColor = (filePath) => {
    if (!filePath) return 'text-gray-900';

    const depth = (filePath.match(/\//g) || []).length;

    // Color scale from darkest to lightest based on depth
    const colors = [
      'text-gray-900', // Root (darkest)
      'text-gray-800',
      'text-gray-700',
      'text-gray-600',
      'text-gray-500' // Deepest (lightest)
    ];

    // Cap at the maximum available color
    const colorIndex = Math.min(depth, colors.length - 1);
    return colors[colorIndex];
  };

  // Get background color based on directory depth
  const getDirectoryBgColor = (filePath) => {
    if (!filePath) return '';

    const depth = (filePath.match(/\//g) || []).length;

    // Background colors based on depth (very subtle)
    const bgColors = [
      '', // Root (no special bg)
      'bg-blue-50',
      'bg-indigo-50',
      'bg-purple-50',
      'bg-pink-50'
    ];

    // Only apply background color for deeper levels
    if (depth === 0) return '';

    // Cap at the maximum available color
    const colorIndex = Math.min(depth, bgColors.length - 1);
    return bgColors[colorIndex];
  };

  // Check if a file is in the selected directory
  const isInSelectedDirectory = (filePath) => {
    if (selectedDirectory === '/') return true; // Root shows all

    // Check if the file path starts with the selected directory
    return filePath.startsWith(selectedDirectory + '/');
  };

  return (
    <div className="p-3 w-full overflow-x-hidden">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-xl font-bold">Minimum Viable Code Description (MVCD)</h1>
        {getActiveProjectName() && (
          <div className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
            Project: {getActiveProjectName()}
          </div>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">Loading MVCD status...</div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex flex-col">
            <div className="text-red-700 mb-2">{error}</div>
            {error.includes('No project selected') && (
              <button
                onClick={handleProjectSelection}
                className="self-start inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go to Projects
              </button>
            )}
          </div>
        </div>
      ) : !getActiveProject() ? (
        <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
          <div className="flex flex-col">
            <div className="text-yellow-700 mb-2">No project selected. Please select a project first.</div>
            <button
              onClick={handleProjectSelection}
              className="self-start inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Go to Projects
            </button>
          </div>
        </div>
      ) : (
        <div>
          {/* MVCD Workflow - Moved up from the workflow tab */}
          <div className="bg-gray-50 shadow overflow-hidden sm:rounded-lg mb-3 border border-gray-300">
            <div className="px-3 py-2">
              <h3 className="text-base font-medium text-gray-900">MVCD Workflow</h3>
              <p className="mt-1 max-w-2xl text-xs text-gray-500">
                Three steps to build a comprehensive understanding of your codebase.
              </p>

              <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
                {/* Step 1: Generate MVCD */}
                <div className={`p-2 rounded-lg border ${mvcdStatus?.mvcd_exists ? 'bg-green-50 border-green-300' : 'bg-gray-100 border-gray-300'}`}>
                  <div className="flex items-center">
                    <div className="mr-2">
                      {/* Show green status if MVCD exists, otherwise show task status */}
                      {renderStatusIndicator(tasks.generate?.status, mvcdStatus?.mvcd_exists ? 'completed' : null)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 1: Analyze Codebase</h4>
                      <p className="text-xs text-gray-500">
                        Scans files, elements, dependencies, and LOC.
                      </p>
                    </div>
                    <div>
                      <button
                        onClick={handleGenerateMvcd}
                        disabled={tasks.generate?.status === 'running' || mvcdStatus?.mvcd_exists}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          tasks.generate?.status === 'running' || mvcdStatus?.mvcd_exists
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                        title={mvcdStatus?.mvcd_exists ? 'MVCD file already exists' : ''}
                      >
                        {tasks.generate?.status === 'running'
                          ? 'Running...'
                          : mvcdStatus?.mvcd_exists
                            ? 'Done'
                            : 'Start'}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Step 2: Enrich MVCD */}
                <div className="bg-gray-100 p-2 rounded-lg border border-gray-300">
                  <div className="flex items-center">
                    <div className="mr-2">
                      {renderStatusIndicator(tasks.enrich?.status)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 2: Enrich MVCD</h4>
                      <p className="text-xs text-gray-500">
                        Uses {codingAgent} to generate descriptions.
                      </p>
                    </div>
                    <div>
                      <button
                        onClick={handleEnrichMvcd}
                        disabled={!mvcdStatus?.mvcd_exists || tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          !mvcdStatus?.mvcd_exists || tasks.enrich?.status === 'running' || tasks.enrich?.status === 'waiting_for_user'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {tasks.enrich?.status === 'running'
                          ? 'Running...'
                          : tasks.enrich?.status === 'waiting_for_user'
                            ? 'Wait'
                            : 'Start'}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Step 3: Analyze MVCD */}
                <div className="bg-gray-100 p-2 rounded-lg border border-gray-300">
                  <div className="flex items-center">
                    <div className="mr-2">
                      {renderStatusIndicator(tasks.analyze?.status)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium">Step 3: Improvement Analysis</h4>
                      <p className="text-xs text-gray-500">
                        Identifies improvement opportunities.
                      </p>
                    </div>
                    <div>
                      <button
                        onClick={handleAnalyzeMvcd}
                        disabled={!mvcdStatus?.mvcd_exists || tasks.analyze?.status === 'running'}
                        className={`px-2 py-1 rounded text-xs font-medium text-white ${
                          !mvcdStatus?.mvcd_exists || tasks.analyze?.status === 'running'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {tasks.analyze?.status === 'running' ? 'Running...' : 'Start'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Agent selection */}
              <div className="mt-2 flex items-center">
                <label htmlFor="coding-agent" className="mr-1 text-xs font-medium text-gray-700">
                  Agent:
                </label>
                <select
                  id="coding-agent"
                  value={codingAgent}
                  onChange={handleCodingAgentChange}
                  className="block w-28 pl-2 pr-6 py-0.5 text-xs border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
                  disabled={tasks.enrich?.status === 'running'}
                  title="Select which coding agent to use for enriching MVCD descriptions"
                >
                  <option value="augment">Augment</option>
                  <option value="cursor">Cursor</option>
                  <option value="chatgpt">ChatGPT</option>
                </select>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-300 mb-3 bg-gray-100 rounded-t-lg">
            <nav className="-mb-px flex space-x-4 px-2 pt-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`${
                  activeTab === 'overview'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('codebase')}
                className={`${
                  activeTab === 'codebase'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Code Base
              </button>
              <button
                onClick={() => setActiveTab('metrics')}
                className={`${
                  activeTab === 'metrics'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                Metrics
              </button>
              <button
                onClick={() => setActiveTab('ignore')}
                className={`${
                  activeTab === 'ignore'
                    ? 'border-gray-700 bg-white text-gray-900 rounded-t-lg shadow-sm'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-400 hover:bg-gray-50'
                } whitespace-nowrap py-2 px-3 border-b-2 font-medium text-xs`}
              >
                .ignore
              </button>
            </nav>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">MVCD Status</h3>
                  <div className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">MVCD File</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {mvcdStatus?.mvcd_exists ? 'Exists' : 'Not Found'}
                        </dd>
                      </div>
                    </div>
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">Last Updated</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {formatDate(mvcdStatus?.last_updated)}
                        </dd>
                      </div>
                    </div>
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Entries</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {mvcdStatus?.total_entries || 0}
                        </dd>
                      </div>
                    </div>
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">Entries with Descriptions</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {mvcdStatus?.entries_with_descriptions || 0} / {mvcdStatus?.total_entries || 0}
                        </dd>
                      </div>
                    </div>
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">Average Confidence</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {formatPercentage(mvcdStatus?.average_confidence || 0)}
                        </dd>
                      </div>
                    </div>
                    <div className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Lines of Code</dt>
                        <dd className="mt-1 text-3xl font-semibold text-gray-900">
                          {mvcdStatus?.total_loc || 0}
                        </dd>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Code Base Tab */}
          {activeTab === 'codebase' && (
            <div>
              <div className="bg-gray-50 shadow overflow-hidden sm:rounded-lg mb-3 border border-gray-300 w-full">
                <div className="px-3 py-2">
                  <h3 className="text-base font-medium text-gray-900">Code Base Structure</h3>

                  {/* Directory Focus Selection */}
                  <div className="mt-2 mb-3">
                    <label htmlFor="directory-focus" className="block text-xs font-medium text-gray-700 mb-1">
                      Directory Focus
                    </label>
                    <select
                      id="directory-focus"
                      value={selectedDirectory}
                      onChange={handleDirectoryChange}
                      className="block w-full pl-2 pr-8 py-1 text-xs border-gray-400 bg-white focus:outline-none focus:ring-gray-500 focus:border-gray-500 rounded-md shadow-sm"
                    >
                      {directoryStructure.map((dir) => {
                        // Calculate indentation for display
                        const indentLevel = (dir.match(/\//g) || []).length;
                        const indentStr = '\u00A0\u00A0'.repeat(indentLevel);
                        const displayName = dir === '/' ? 'All Directories' : dir.split('/').pop();

                        return (
                          <option key={dir} value={dir}>
                            {indentStr}{displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>

                  {/* MVCD Content Display */}
                  {mvcdLoading ? (
                    <div className="flex justify-center items-center h-64">
                      <div className="text-gray-500">Loading MVCD data...</div>
                    </div>
                  ) : mvcdError ? (
                    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                      <div className="text-red-700">{mvcdError}</div>
                      <button
                        onClick={fetchMvcdData}
                        className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Retry
                      </button>
                    </div>
                  ) : !mvcdData ? (
                    <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
                      <div className="text-yellow-700">No MVCD data available. Please generate MVCD first.</div>
                      <button
                        onClick={fetchMvcdData}
                        className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Load MVCD Data
                      </button>
                    </div>
                  ) : (
                    <div className="border border-gray-300 rounded-md overflow-hidden shadow-sm w-full">
                      {/* Header */}
                      <div className="flex bg-gray-200 p-1 border-b border-gray-300 font-medium text-xs text-gray-700">
                        <div className="w-[12%] text-left">Element</div>
                        <div className="w-[15%] text-left">Path</div>
                        <div className="w-[7%] text-left">Type</div>
                        <div className="w-[6%] text-left">Status</div>
                        <div className="w-[30%] text-left">Description</div>
                        <div className="w-[10%] text-left">Dependencies</div>
                        <div className="w-[5%] text-right">Conf</div>
                        <div className="w-[5%] text-right">LOC</div>
                        <div className="w-[10%] text-right pr-2">Modified</div>
                      </div>

                      {/* Scrollable Content */}
                      <div className="overflow-y-auto" style={{ maxHeight: '600px' }}>
                        {mvcdData.codebase
                          .filter(item => isInSelectedDirectory(item.file))
                          .map((item, index) => {
                            // Calculate indentation level for display
                            const indentLevel = getIndentationLevel(item.file);
                            const indentStr = '\u00A0\u00A0'.repeat(indentLevel);

                            // Extract file name from path
                            const fileName = item.file.split('/').pop();

                            // Get element name
                            const elementName = item.element || 'Unknown';

                            // Get status (active/deprecated)
                            const status = item.status || 'active';

                            // Get confidence level
                            const confidence = item.confidence || 0;

                            // Get lines of code
                            const loc = item.loc || 0;

                            // Get description
                            const description = item.description || 'No description available';

                            // Get last modified timestamp
                            const lastModified = item.last_modified || null;

                            return (
                              <div
                                key={`${item.file}-${elementName}-${index}`}
                                className={`flex items-center py-0.5 px-1 text-xs ${index % 2 === 0 ? 'bg-white' : 'bg-gray-100'} ${getDirectoryBgColor(item.file)} border-b border-gray-300 hover:bg-blue-100`}
                              >
                                <div className={`w-[12%] truncate text-left ${getDirectoryColor(item.file)}`} title={`${item.file}:${elementName}`}>
                                  {indentStr}{elementName}
                                </div>
                                <div className={`w-[15%] truncate text-left ${getDirectoryColor(item.file)}`} title={item.file}>
                                  {item.file}
                                </div>
                                <div className="w-[7%] truncate text-left" title={item.type || 'Not specified'}>
                                  {item.type || '-'}
                                </div>
                                <div className="w-[6%] whitespace-nowrap text-left">
                                  <span className={`inline-block px-1 text-xs font-medium rounded-full ${
                                    status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {status}
                                  </span>
                                </div>
                                <div className="w-[30%] truncate text-left" title={description}>
                                  {description.substring(0, 50)}{description.length > 50 ? '...' : ''}
                                </div>
                                <div className="w-[10%] truncate text-left" title={item.dependencies ? item.dependencies.join(', ') : 'None'}>
                                  {item.dependencies && item.dependencies.length > 0
                                    ? item.dependencies.join(', ').substring(0, 20) + (item.dependencies.join(', ').length > 20 ? '...' : '')
                                    : '-'}
                                </div>
                                <div className="w-[5%] text-right whitespace-nowrap">
                                  <span className={`inline-block px-1 text-xs font-medium rounded-full ${
                                    confidence >= 70 ? 'bg-green-100 text-green-800' :
                                    confidence > 0 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {confidence}%
                                  </span>
                                </div>
                                <div className="w-[5%] text-right whitespace-nowrap">
                                  {loc}
                                </div>
                                <div className="w-[10%] text-right text-gray-500 text-xs truncate whitespace-nowrap pr-2" title={lastModified ? new Date(lastModified * 1000).toLocaleString() : 'Not available'}>
                                  {lastModified ? formatDate(lastModified) : 'N/A'}
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Metrics Tab */}
          {activeTab === 'metrics' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Codebase Metrics</h3>

                  <div className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2">
                    {/* Frontend Metrics */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-lg font-medium mb-4">Frontend</h4>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Entries</span>
                            <span className="text-sm font-medium text-gray-900">{mvcdStatus?.frontend_entries || 0}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Lines of Code</span>
                            <span className="text-sm font-medium text-gray-900">{mvcdStatus?.frontend_loc || 0}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Confidence</span>
                            <span className="text-sm font-medium text-gray-900">{formatPercentage(mvcdStatus?.frontend_confidence || 0)}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full"
                              style={{ width: `${mvcdStatus?.frontend_confidence || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Backend Metrics */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-lg font-medium mb-4">Backend</h4>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Entries</span>
                            <span className="text-sm font-medium text-gray-900">{mvcdStatus?.backend_entries || 0}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Lines of Code</span>
                            <span className="text-sm font-medium text-gray-900">{mvcdStatus?.backend_loc || 0}</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-gray-500">Confidence</span>
                            <span className="text-sm font-medium text-gray-900">{formatPercentage(mvcdStatus?.backend_confidence || 0)}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                            <div
                              className="bg-green-600 h-2.5 rounded-full"
                              style={{ width: `${mvcdStatus?.backend_confidence || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Ignore Tab */}
          {activeTab === 'ignore' && (
            <div>
              <div className="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">MVCD Ignore Patterns</h3>
                    {ignoreModified && (
                      <button
                        onClick={handleSaveIgnoreChanges}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Save Changes
                      </button>
                    )}
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600">
                      Configure which files and directories to exclude from MVCD analysis.
                      Uncheck patterns to include them in the codebase scan.
                    </p>
                  </div>

                  {ignoreLoading && (
                    <div className="flex justify-center items-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <span className="ml-2 text-gray-600">Loading ignore patterns...</span>
                    </div>
                  )}

                  {ignoreError && (
                    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                      <div className="text-red-700">{ignoreError}</div>
                    </div>
                  )}

                  {ignoreData && !ignoreLoading && (
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                      <table className="min-w-full divide-y divide-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Enabled
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Pattern
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {ignoreData.map((item) => {
                            // Generate description based on pattern
                            const getPatternDescription = (pattern) => {
                              if (pattern.includes('node_modules')) return 'Node.js dependencies';
                              if (pattern.includes('__pycache__')) return 'Python cache files';
                              if (pattern.includes('.git/')) return 'Git repository files';
                              if (pattern.includes('.venv/')) return 'Python virtual environment';
                              if (pattern.includes('*.test.')) return 'Test files';
                              if (pattern.includes('*.spec.')) return 'Specification test files';
                              if (pattern.includes('*.stories.')) return 'Storybook files';
                              if (pattern.includes('*.svg') || pattern.includes('*.png') || pattern.includes('*.ico')) return 'Image files';
                              if (pattern.includes('*.css') || pattern.includes('*.scss')) return 'Stylesheet files';
                              if (pattern.includes('*.lock')) return 'Lock files';
                              if (pattern.includes('.env')) return 'Environment files';
                              if (pattern.includes('__init__.py')) return 'Python package init files';
                              if (pattern.includes('assets/') || pattern.includes('styles/')) return 'Static assets';
                              return 'Custom ignore pattern';
                            };

                            return (
                              <tr key={item.id} className={item.enabled ? '' : 'bg-gray-50 opacity-60'}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <input
                                    type="checkbox"
                                    checked={item.enabled}
                                    onChange={() => handleIgnoreToggle(item.id)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm font-mono text-gray-900">{item.pattern}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm text-gray-500">{getPatternDescription(item.pattern)}</div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {ignoreModified && (
                    <div className="mt-4 bg-yellow-50 border-l-4 border-yellow-500 p-4">
                      <div className="text-yellow-700 text-sm">
                        You have unsaved changes. Click "Save Changes" to apply your modifications.
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MVCD;
